#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2025/8/12 22:37
# <AUTHOR> Groot
# @File    : rewrite_resp.py
# @Software: PyCharm

# Windows 下 mitmproxy/mitmweb 加载本脚本后，将匹配到的请求的响应体按你的规则改写。
# 运行方式：
#   mitmweb  -p 8080 -s C:\path\to\rewrite_resp.py
#   mitmdump -p 8080 -s C:\path\to\rewrite_resp.py


import json
from mitmproxy import http

FIXED_JSON_BODY = {
    "code": 0,
    "msg": "success",
    "data": []
}


def request(flow: http.HTTPFlow):
    if flow.request.path == "/dataManagement/api/inner/getLatestUpgradePackage":
        print("GOT UUUUUUUUUUU", flow.request.url)


def response(flow: http.HTTPFlow):
    assert flow.response
    if flow.request.path == "/dataManagement/api/inner/getLatestUpgradePackage":
        flow.response.text = json.dumps(FIXED_JSON_BODY)
        print("REPLACE.........")
