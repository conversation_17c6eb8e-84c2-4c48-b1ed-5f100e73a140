# parse_jsonl_to_csv.py
# -*- coding: utf-8 -*-

import argparse
import json
import sys
import re
from pathlib import Path
from typing import Any, Dict, Iterable, List, Tuple

import pandas as pd
from pandas import json_normalize


def read_json_lines(path: Path) -> Iterable[str]:
    """逐行读取文件，去除每行首尾空白，跳过空行。"""
    with path.open("r", encoding="utf-8", errors="ignore") as f:
        for ln, line in enumerate(f, 1):
            s = line.strip()
            if not s:
                continue
            yield s


def try_parse_json(line: str, ln: int) -> Dict[str, Any]:
    """将一行文本解析为 JSON 对象；失败则抛出带行号的异常。"""
    try:
        return json.loads(line)
    except json.JSONDecodeError as e:
        # 如果末尾有逗号或 BOM/奇怪字符，给出更友好提示
        msg = f"[Line {ln}] JSON 解析失败: {e.msg} (pos {e.pos})\n片段: {line[:200]}"
        raise ValueError(msg) from e


def dedup_columns(cols: Iterable[str]) -> List[str]:
    """处理拍扁后可能出现的重复列名，追加后缀 .1 .2 ..."""
    seen = {}
    out = []
    for c in cols:
        base = c
        if c in seen:
            seen[c] += 1
            c = f"{base}.{seen[base]}"
        else:
            seen[c] = 0
        out.append(c)
    return out


_mem_pat = re.compile(
    r"Available:\s*([\d.]+)\s*GiB\s*/\s*([\d.]+)\s*GiB",
    re.IGNORECASE,
)


def parse_mem_gib(s: Any) -> Tuple[Any, Any]:
    """从 'Available: 10.0 GiB/15.6 GiB' 解析为 (10.0, 15.6)。解析失败返回 (None, None)。"""
    if not isinstance(s, str):
        return (None, None)
    m = _mem_pat.search(s)
    if not m:
        return (None, None)
    try:
        return (float(m.group(1)), float(m.group(2)))
    except Exception:
        return (None, None)


def normalize_records(records: List[Dict[str, Any]]) -> pd.DataFrame:
    """拍扁 JSON 记录并清洗。"""
    # 拍扁
    df = json_normalize(records, sep=".")
    # 去重列名
    df.columns = dedup_columns(df.columns)
    # 解析 memoryInfo
    if "memoryInfo" in df.columns:
        mem_cols = df["memoryInfo"].apply(parse_mem_gib)
        df["mem.available_gib"] = mem_cols.apply(lambda t: t[0])
        df["mem.total_gib"] = mem_cols.apply(lambda t: t[1])
    # 去除每个单元格（仅字符串）首尾空白
    df = df.applymap(lambda x: x.strip() if isinstance(x, str) else x)
    return df


def main(argv=None):
    parser = argparse.ArgumentParser(
        description="按行解析 JSON（每行一个 JSON 对象）并导出 CSV。"
    )
    parser.add_argument(
        "-i", "--input", required=True, help="输入文本文件路径（每行一个 JSON）"
    )
    parser.add_argument(
        "-o", "--output", required=True, help="输出 CSV 文件路径"
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="仅解析前 N 行（调试用）",
    )
    args = parser.parse_args(argv)

    in_path = Path(args.input)
    out_path = Path(args.output)

    if not in_path.exists():
        print(f"输入文件不存在: {in_path}", file=sys.stderr)
        sys.exit(1)

    records: List[Dict[str, Any]] = []
    for ln, line in enumerate(read_json_lines(in_path), 1):
        if args.limit and ln > args.limit:
            break
        obj = try_parse_json(line, ln)
        records.append(obj)

    if not records:
        print("未读取到任何 JSON 记录（可能都是空行）。", file=sys.stderr)
        # 也写出一个空 CSV，列为空
        pd.DataFrame().to_csv(out_path, index=False, encoding="utf-8-sig")
        print(f"已写出空 CSV: {out_path}")
        return

    df = normalize_records(records)

    # 写出 CSV（utf-8-sig 便于 Excel 打开中文不乱码）
    out_path.parent.mkdir(parents=True, exist_ok=True)
    df.to_csv(out_path, index=False, encoding="utf-8-sig")
    print(f"完成：{len(df)} 行，{len(df.columns)} 列 -> {out_path}")


if __name__ == "__main__":
    main()
